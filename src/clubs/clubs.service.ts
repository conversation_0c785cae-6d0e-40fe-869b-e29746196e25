import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { PaginateModel, FilterQuery } from 'mongoose';
import { CreateClubInput } from './dto/create-club.input';
import { UpdateClubInput } from './dto/update-club.input';
import { Club } from './entities/club.entity';
import { PaginationInput } from 'src/common/pagination.input';

@Injectable()
export class ClubsService {
  constructor(
    @InjectModel(Club.name)
    private club: PaginateModel<Club>,
  ) {}

  async create(createClubInput: CreateClubInput) {
    return this.club.create(createClubInput);
  }

  async findAll(
    filter: FilterQuery<Club> = {},
    paginationInput?: PaginationInput,
  ) {
    return this.club.paginate(filter, paginationInput);
  }

  async findOne(id: string) {
    return this.club.findById(id);
  }

  async update(id: string, updateClubInput: UpdateClubInput) {
    return this.club.findByIdAndUpdate(id, updateClubInput, { new: true });
  }

  async remove(id: string) {
    return this.club.findByIdAndDelete(id);
  }
}
