import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ClubsService } from './clubs.service';
import { ClubsResolver } from './clubs.resolver';
import { Club, ClubSchema } from './entities/club.entity';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Club.name, schema: ClubSchema }]),
  ],
  providers: [ClubsResolver, ClubsService],
})
export class ClubsModule {}
