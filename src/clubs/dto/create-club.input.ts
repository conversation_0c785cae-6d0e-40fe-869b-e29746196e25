import { InputType } from '@nestjs/graphql';
import {
  AddressInput,
  DayOfWeek,
  DayTimingInput,
} from 'src/common/common.entity';
import { ZodValidation } from 'src/common/zod-validator/decorators/zod-validation.decorator';
import { ContactInput } from 'src/users/dto/create-user.input';
import { z } from 'zod';
import { ClubStatus } from '../entities/club.entity';

const createClubInputSchema = z.object({
  name: z.string(),
  description: z.string(),
  categories: z.array(z.string()),
  status: z.nativeEnum(ClubStatus),
  logo: z.string().optional(),
  coverImage: z.string().optional(),
  images: z.array(z.string()),
  phone: z
    .object({
      countryCode: z.string(),
      phone: z.string(),
    })
    .optional(),
  address: z
    .object({
      address: z.string(),
      coordinates: z.object({
        latitude: z.number(),
        longitude: z.number(),
      }),
      metroLine: z.string().optional(),
      metroStation: z.string().optional(),
    })
    .optional(),
  openingHours: z
    .object({
      schedule: z.array(
        z.object({
          day: z.nativeEnum(DayOfWeek),
          openingTime: z.string().optional(),
          closingTime: z.string().optional(),
        }),
      ),
    })
    .optional(),
  featured: z.boolean(),
  rating: z.number().min(0).max(5),
});

@InputType()
export class OpeningHoursInput {
  /** Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format */
  schedule: DayTimingInput[];
}

@ZodValidation(createClubInputSchema)
@InputType()
export class CreateClubInput {
  /** Club name */
  name: string;

  /** Club description */
  description: string;

  /** Club category IDs */
  categories: string[];

  /** Club status */
  status: ClubStatus;

  /** Club logo URL */
  logo?: string;

  /** Club cover image URL */
  coverImage?: string;

  /** Array of club image URLs */
  images: string[];

  /** Club contact information (phone) */
  contact?: ContactInput;

  /** Club address */
  address?: AddressInput;

  /** Opening hours */
  openingHours?: OpeningHoursInput;

  /** Whether club is featured */
  featured: boolean;

  /** Club rating (0-5) */
  rating: number;
}
