import { Args, Query, Resolver } from '@nestjs/graphql';
import { UsersInput } from './dto/users.input';
import { PaginatedUsers, User } from './entities/user.entity';
import { UsersService } from './users.service';

@Resolver(() => User)
export class UsersResolver {
  constructor(private readonly usersService: UsersService) {}

  @Query(() => PaginatedUsers, { name: 'users' })
  findAll(
    @Args('usersInput', { type: () => UsersInput, nullable: true })
    usersInput?: UsersInput,
  ) {
    const filter = {};

    return this.usersService.findAll(filter, usersInput?.pagination);
  }

  @Query(() => User, { name: 'user' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.usersService.findOne({ _id: id });
  }
}
