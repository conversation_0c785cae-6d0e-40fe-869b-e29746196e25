import { InputType } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { PaginationInput } from 'src/common/pagination.input';

@InputType()
export class UsersInput extends PaginationInput {
  /** Pagination options */
  @IsOptional()
  @ValidateNested()
  @Type(() => PaginationInput)
  pagination?: PaginationInput;
}
