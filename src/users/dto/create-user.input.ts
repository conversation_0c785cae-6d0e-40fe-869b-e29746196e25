import { InputType } from '@nestjs/graphql';
import { UserRoles, UserStatus } from '../entities/user.entity';
import {
  IsString,
  IsEnum,
  MinLength,
  IsEmail,
  IsBoolean,
  ValidateNested,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';

@InputType()
export class ContactInput {
  /** Country code (e.g., +1, +91) */
  @IsString()
  countryCode: string;

  /** Phone number */
  @IsString()
  phone: string;
}

@InputType()
export class CreateUserInput {
  /** User fullname */
  @IsString()
  fullname: string;

  /** User phone number */
  @IsEmail()
  @Transform(({ value }) => (value as string).toLowerCase())
  email: string;

  /** User role */
  @IsEnum(UserRoles)
  role: UserRoles;

  /** User password */
  @IsString()
  @MinLength(4)
  password: string;

  /** User active status */
  @IsEnum(UserStatus)
  userStatus: UserStatus;

  /** User contact information */
  @ValidateNested()
  @Type(() => ContactInput)
  contact: ContactInput;

  /** Whether user has accepted terms and conditions */
  @IsBoolean()
  acceptedTermsAndConditions: boolean;

  /** Whether user wants to receive discounts, royalty offers and updates */
  @IsBoolean()
  subscribeToUpdates: boolean;
}
