import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { AwsService } from './aws.service';
import { SignedUploadUrlInput } from './dto/signed-upload-url.input';
import { SignedUploadUrl } from './entities/signed-upload-url.entity';
import { UseGuards } from '@nestjs/common';
import { RolesGuard } from 'src/auth/guards/roles.guard';
import { Roles } from 'src/auth/decorators/roles.decorator';
import { UserRoles } from 'src/users/entities/user.entity';

@Resolver(() => SignedUploadUrl)
export class AwsResolver {
  constructor(private readonly awsService: AwsService) {}

  @Mutation(() => SignedUploadUrl)
  @UseGuards(RolesGuard)
  @Roles(UserRoles.ADMIN)
  createSignedUploadUrl(
    @Args('input') input: SignedUploadUrlInput,
  ): Promise<SignedUploadUrl> {
    return this.awsService.getSignedUploadUrl(
      input.key,
      input.contentType,
      input.expiresIn,
    );
  }
}
