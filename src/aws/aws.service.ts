import { Injectable, NotFoundException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as AWS from 'aws-sdk';
import { String } from 'aws-sdk/clients/acm';

@Injectable()
export class AwsService {
  private s3: AWS.S3;
  private rekognition: AWS.Rekognition;

  constructor(private readonly configService: ConfigService) {
    const awsConfig = {
      accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID'),
      secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY'),
      region: 'ap-southeast-1',
    };

    (AWS.config as AWS.Config).update(awsConfig);
    this.s3 = new AWS.S3();
    this.rekognition = new AWS.Rekognition();
  }

  async uploadFile(
    file: Express.Multer.File,
    key: string,
  ): Promise<{ url: string }> {
    const params = {
      Bucket:
        this.configService.get<string>('AWS_BUCKET') ||
        (() => {
          throw new Error('AWS_BUCKET is not defined');
        })(),
      Key: key,
      Body: file.buffer,
      ContentType: file.mimetype,
      ACL: 'public-read',
    };

    await this.s3.upload(params).promise();

    return {
      url: `${this.configService.get('AWS_BUCKET_URL')}/${key}`,
    };
  }

  async uploadImageBuffer(
    imageBuffer: Buffer,
    key: string,
    contentType: string = 'image/jpeg',
  ): Promise<{ url: string }> {
    // Ensure key doesn't start with a slash
    const sanitizedKey = key.replace(/^\//, '');

    const params = {
      Bucket:
        this.configService.get<string>('AWS_BUCKET') ||
        (() => {
          throw new Error('AWS_BUCKET is not defined');
        })(),
      Key: sanitizedKey,
      Body: imageBuffer,
      ContentType: contentType,
      ACL: 'public-read',
    };

    await this.s3.upload(params).promise();

    // Construct URL properly to avoid double slashes
    const bucketUrl = this.configService
      .get<string>('AWS_BUCKET_URL')!
      .replace(/\/$/, '');

    const url = `${bucketUrl}/${sanitizedKey}`;

    return { url };
  }

  async deleteFile(key: string): Promise<void> {
    const bucket = this.configService.get<string>('AWS_BUCKET');
    if (!bucket) {
      throw new Error('AWS_BUCKET is not defined');
    }

    const params = {
      Bucket: bucket,
      Key: key,
    };

    await this.s3.deleteObject(params).promise();
  }

  async indexFace(
    imageBuffer: Buffer,
    externalImageId: string,
  ): Promise<AWS.Rekognition.IndexFacesResponse> {
    const params = {
      CollectionId:
        this.configService.get<string>('AWS_REKOGNITION_COLLECTION_ID') ||
        (() => {
          throw new Error('AWS_REKOGNITION_COLLECTION_ID is not defined');
        })(),
      Image: {
        Bytes: imageBuffer,
      },
      ExternalImageId: externalImageId,
      MaxFaces: 1,
      QualityFilter: 'AUTO',
      DetectionAttributes: ['ALL'],
    };

    return this.rekognition.indexFaces(params).promise();
  }

  async searchFace(
    imageBuffer: Buffer,
  ): Promise<AWS.Rekognition.SearchFacesByImageResponse> {
    const params = {
      CollectionId:
        this.configService.get<string>('AWS_REKOGNITION_COLLECTION_ID') ||
        (() => {
          throw new Error('AWS_REKOGNITION_COLLECTION_ID is not defined');
        })(),
      Image: {
        Bytes: imageBuffer,
      },
      MaxFaces: 1,
      FaceMatchThreshold: 95,
    };

    return this.rekognition.searchFacesByImage(params).promise();
  }

  async deleteFace(faceId: string): Promise<void> {
    const collectionId = this.configService.get<string>(
      'AWS_REKOGNITION_COLLECTION_ID',
    );
    if (!collectionId) {
      throw new NotFoundException(
        'AWS_REKOGNITION_COLLECTION_ID is not defined',
      );
    }

    const params = {
      CollectionId: collectionId,
      FaceIds: [faceId],
    };

    await this.rekognition.deleteFaces(params).promise();
  }

  async getSignedUploadUrl(
    key: string,
    contentType: string,
    expiresIn: number = 300,
  ): Promise<{ url: string; fields: any }> {
    const bucket = this.configService.get<string>('AWS_BUCKET');
    if (!bucket) {
      throw new Error('AWS_BUCKET is not defined');
    }

    const params = {
      Bucket: bucket,
      Fields: {
        key,
        'Content-Type': contentType,
        acl: 'public-read',
      },
      Conditions: [
        ['content-length-range', 0, 1073741824], // 1GB max file size (1024 * 1024 * 1024 bytes)
        ['starts-with', '$Content-Type', ''],
        { acl: 'public-read' },
        { bucket },
        ['starts-with', '$key', ''],
      ],
      Expires: expiresIn,
    };

    try {
      const data = await new Promise<AWS.S3.PresignedPost>(
        (resolve, reject) => {
          this.s3.createPresignedPost(params, (err, data) => {
            if (err) reject(err);
            resolve(data);
          });
        },
      );

      // Construct the complete form data that needs to be sent
      return {
        url: data.url,
        fields: {
          key,
          bucket,
          acl: 'public-read',
          algorithm: data.fields['X-Amz-Algorithm'],
          credential: data.fields['X-Amz-Credential'],
          date: data.fields['X-Amz-Date'],
          Policy: data.fields.Policy,
          signature: data.fields['X-Amz-Signature'],
          'Content-Type': contentType,
        },
      };
    } catch (error) {
      console.error('Error generating presigned URL:', error);
      throw error;
    }
  }
}
