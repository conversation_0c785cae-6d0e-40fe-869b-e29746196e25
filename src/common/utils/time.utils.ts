import { format, parse, isValid } from 'date-fns';

/**
 * Converts HHMM number format to formatted time string
 * @param timeNumber - Time in HHMM format (e.g., 900, 1730, 2359)
 * @param formatString - Output format (default: 'h:mm a' for 12-hour format)
 * @returns Formatted time string (e.g., '9:00 AM', '5:30 PM')
 *
 * @example
 * convertNumberToTime(900) // '9:00 AM'
 * convertNumberToTime(1730) // '5:30 PM'
 * convertNumberToTime(0) // '12:00 AM'
 * convertNumberToTime(1200) // '12:00 PM'
 * convertNumberToTime(900, 'HH:mm') // '09:00' (24-hour format)
 */
export function convertNumberToTime(
  timeNumber: number,
  formatString: string = 'h:mm a',
): string {
  // Validate input
  if (timeNumber < 0 || timeNumber > 2359) {
    throw new Error('Time must be between 0000 and 2359');
  }

  // Convert number to hours and minutes
  const hours = Math.floor(timeNumber / 100);
  const minutes = timeNumber % 100;

  // Validate hours and minutes
  if (hours > 23 || minutes > 59) {
    throw new Error(
      'Invalid time format. Hours must be 0-23, minutes must be 0-59',
    );
  }

  // Create a date object with the time (using today's date as base)
  const baseDate = new Date();
  baseDate.setHours(hours, minutes, 0, 0);

  // Format and return
  return format(baseDate, formatString);
}

/**
 * Converts formatted time string to HHMM number format
 * @param timeString - Time string (e.g., '9:00 AM', '5:30 PM', '09:00', '17:30')
 * @param inputFormat - Input format (optional, will try common formats if not provided)
 * @returns Time in HHMM number format
 *
 * @example
 * convertTimeToNumber('9:00 AM') // 900
 * convertTimeToNumber('5:30 PM') // 1730
 * convertTimeToNumber('12:00 AM') // 0
 * convertTimeToNumber('12:00 PM') // 1200
 * convertTimeToNumber('09:00') // 900
 * convertTimeToNumber('17:30') // 1730
 */
export function convertTimeToNumber(
  timeString: string,
  inputFormat?: string,
): number {
  let parsedDate: Date | undefined = undefined;

  if (inputFormat) {
    // Use provided format
    parsedDate = parse(timeString, inputFormat, new Date());
  } else {
    // Try common formats
    const commonFormats = [
      'h:mm a', // 9:00 AM
      'h:mma', // 9:00AM
      'h a', // 9 AM
      'ha', // 9AM
      'HH:mm', // 09:00, 17:30
      'H:mm', // 9:00, 17:30
      'HH:mm:ss', // 09:00:00
      'H:mm:ss', // 9:00:00
    ];

    for (const format of commonFormats) {
      const tryDate = parse(timeString, format, new Date());
      if (isValid(tryDate)) {
        parsedDate = tryDate;
        break;
      }
    }
  }

  if (!parsedDate || !isValid(parsedDate)) {
    throw new Error(`Unable to parse time string: ${timeString}`);
  }

  // Extract hours and minutes
  const hours = parsedDate.getHours();
  const minutes = parsedDate.getMinutes();

  // Convert to HHMM format
  return hours * 100 + minutes;
}

/**
 * Validates if a number is a valid time in HHMM format
 * @param timeNumber - Time number to validate
 * @returns boolean indicating if the time is valid
 *
 * @example
 * isValidTimeNumber(900) // true
 * isValidTimeNumber(2359) // true
 * isValidTimeNumber(2400) // false (invalid hour)
 * isValidTimeNumber(1260) // false (invalid minute)
 */
export function isValidTimeNumber(timeNumber: number): boolean {
  if (timeNumber < 0 || timeNumber > 2359) {
    return false;
  }

  const hours = Math.floor(timeNumber / 100);
  const minutes = timeNumber % 100;

  return hours <= 23 && minutes <= 59;
}

/**
 * Formats an array of timings (opening hours) to readable format
 * @param timings - Array of time numbers [openTime, closeTime?]
 * @param formatString - Output format (default: 'h:mm a')
 * @returns Formatted string (e.g., '9:00 AM - 5:30 PM' or '9:00 AM (Open)')
 *
 * @example
 * formatTimings([900, 1730]) // '9:00 AM - 5:30 PM'
 * formatTimings([900]) // '9:00 AM (Open)'
 * formatTimings([1800, 200]) // '6:00 PM - 2:00 AM'
 */
export function formatTimings(
  timings: number[],
  formatString: string = 'h:mm a',
): string {
  if (!timings || timings.length === 0) {
    return 'Closed';
  }

  if (timings.length === 1) {
    return `${convertNumberToTime(timings[0], formatString)} (Open)`;
  }

  const openTime = convertNumberToTime(timings[0], formatString);
  const closeTime = convertNumberToTime(timings[1], formatString);

  return `${openTime} - ${closeTime}`;
}

/**
 * Converts a schedule array to a readable format
 * @param schedule - Array of DayTiming objects
 * @param formatString - Output format (default: 'h:mm a')
 * @returns Object with day names as keys and formatted times as values
 *
 * @example
 * const schedule = [
 *   { day: 'MONDAY', timings: [900, 1730] },
 *   { day: 'TUESDAY', timings: [900] }
 * ];
 * formatSchedule(schedule)
 * // {
 * //   MONDAY: '9:00 AM - 5:30 PM',
 * //   TUESDAY: '9:00 AM (Open)'
 * // }
 */
export function formatSchedule(
  schedule: Array<{ day: string; timings: number[] }>,
  formatString: string = 'h:mm a',
): Record<string, string> {
  const result: Record<string, string> = {};

  for (const dayTiming of schedule) {
    result[dayTiming.day] = formatTimings(dayTiming.timings, formatString);
  }

  return result;
}

/**
 * Gets current time in HHMM number format
 * @returns Current time as number (e.g., 1430 for 2:30 PM)
 */
export function getCurrentTimeNumber(): number {
  const now = new Date();
  return now.getHours() * 100 + now.getMinutes();
}

/**
 * Checks if current time is within operating hours
 * @param timings - Array of time numbers [openTime, closeTime?]
 * @returns boolean indicating if currently open
 *
 * @example
 * isCurrentlyOpen([900, 1730]) // true if current time is between 9:00 AM and 5:30 PM
 * isCurrentlyOpen([900]) // true if current time is after 9:00 AM
 */
export function isCurrentlyOpen(timings: number[]): boolean {
  if (!timings || timings.length === 0) {
    return false;
  }

  const currentTime = getCurrentTimeNumber();
  const openTime = timings[0];

  if (timings.length === 1) {
    // Only opening time provided, assume open until end of day
    return currentTime >= openTime;
  }

  const closeTime = timings[1];

  // Handle overnight hours (e.g., 1800 to 200 = 6 PM to 2 AM)
  if (closeTime < openTime) {
    return currentTime >= openTime || currentTime <= closeTime;
  }

  // Normal hours (e.g., 900 to 1730 = 9 AM to 5:30 PM)
  return currentTime >= openTime && currentTime <= closeTime;
}
