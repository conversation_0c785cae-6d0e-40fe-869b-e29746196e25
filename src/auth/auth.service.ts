import { Injectable, NotFoundException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { hash, verify } from 'argon2';
import { CreateUserInput } from 'src/users/dto/create-user.input';
import { UsersService } from 'src/users/users.service';
import { SignInInput } from './dto/auth.input';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async signIn(input: SignInInput) {
    const user = await this.usersService.findOne({
      email: input.email,
    });

    if (!user) throw new NotFoundException('phone or password is incorrect');

    const isValid = await verify(user.password, input.password);
    if (!isValid) throw new NotFoundException('phone or password is incorrect');

    const tokenPayload = { userId: user._id.toString() };
    return { access_token: await this.jwtService.signAsync(tokenPayload) };
  }

  async signUp(input: CreateUserInput) {
    const hashedPassword = await hash(input.password);

    const user = await this.usersService.create({
      ...input,
      password: hashedPassword,
    });

    const tokenPayload = { userId: user._id.toString() };
    return { access_token: await this.jwtService.signAsync(tokenPayload) };
  }
}
