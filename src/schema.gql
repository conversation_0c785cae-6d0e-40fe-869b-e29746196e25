# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Coordinates {
  """Latitude"""
  latitude: Float!

  """Longitude"""
  longitude: Float!
}

type Address {
  """Address"""
  address: String!

  """Coordinates"""
  coordinates: Coordinates!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

type Contact {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

type User {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """User fullname"""
  fullname: String!

  """User's email"""
  email: String!

  """User active status"""
  userStatus: UserStatus!

  """User role"""
  role: UserRoles!
  password: String!

  """User contact information"""
  contact: Contact!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

enum UserStatus {
  ACTIVE
  INACTIVE
}

enum UserRoles {
  ADMIN
  USER
}

type PaginatedUsers {
  """Array of documents"""
  docs: [User!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type AuthOutput {
  """Access token"""
  access_token: String!
}

type PresignedFields {
  key: String!
  bucket: String!
  acl: String!
  algorithm: String!
  credential: String!
  date: String!
  Policy: String!
  signature: String!
}

type SignedUploadUrl {
  url: String!
  fields: PresignedFields!
}

type ClubCategory {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Club Category name"""
  name: String!

  """Club Category description"""
  description: String!
}

type DayTiming {
  timings: [Float!]!

  """Day of the week"""
  day: DayOfWeek!
}

enum DayOfWeek {
  MONDAY
  TUESDAY
  WEDNESDAY
  THURSDAY
  FRIDAY
  SATURDAY
  SUNDAY
}

type OpeningHours {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTiming!]!
}

type Club {
  """MongoDB ObjectId"""
  _id: ID!
  id: ID!

  """Document creation timestamp"""
  createdAt: DateTime!

  """Document last update timestamp"""
  updatedAt: DateTime!

  """Club category"""
  categories: [ClubCategory!]!

  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club phone number"""
  phone: Contact

  """Club address"""
  address: Address

  """Opening hours"""
  openingHours: OpeningHours

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

enum ClubStatus {
  ACTIVE
  INACTIVE
  PENDING
  SUSPENDED
}

type PaginatedClubs {
  """Array of documents"""
  docs: [Club!]!

  """Total number of documents"""
  totalDocs: Float!

  """Number of documents per page"""
  limit: Float!

  """Current page number"""
  page: Float!

  """Total number of pages"""
  totalPages: Float!

  """Previous page number"""
  prevPage: Float

  """Next page number"""
  nextPage: Float

  """Whether there is a previous page"""
  hasPrevPage: Boolean!

  """Whether there is a next page"""
  hasNextPage: Boolean!

  """Starting index of documents on current page"""
  pagingCounter: Float
}

type City {
  """Name of the city"""
  name: String!

  """Main image of the city"""
  image: String!

  """Cover image for the city"""
  coverImage: String!

  """Main heading for the city"""
  heading: String!

  """Sub heading for the city"""
  subHeading: String!

  """Geographical coordinates of the city"""
  coords: Coordinates!
}

type Query {
  """Logged in  user"""
  me: User!
  users(usersInput: UsersInput): PaginatedUsers!
  user(id: String!): User!
  clubs(clubsInput: ClubsInput): PaginatedClubs!
  club(id: String!): Club!
  city(id: Int!): City!
}

input UsersInput {
  """Page number (1-based)"""
  page: Float! = 1

  """Number of items per page"""
  limit: Float! = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!]! = [{field: "createdAt", order: DESC}]

  """Pagination options"""
  pagination: PaginationInput
}

input SortConfigInput {
  """Field to sort by"""
  field: String!

  """
  Sort order: "asc" or "desc"
  """
  order: SortOrder! = DESC
}

enum SortOrder {
  ASC
  DESC
}

input PaginationInput {
  """Page number (1-based)"""
  page: Float! = 1

  """Number of items per page"""
  limit: Float! = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!]! = [{field: "createdAt", order: DESC}]
}

input ClubsInput {
  """Page number (1-based)"""
  page: Float! = 1

  """Number of items per page"""
  limit: Float! = 30

  """Sort configuration array"""
  sortConfig: [SortConfigInput!]! = [{field: "createdAt", order: DESC}]
}

type Mutation {
  signIn(input: SignInInput!): AuthOutput!
  signUp(input: CreateUserInput!): AuthOutput!
  createSignedUploadUrl(input: SignedUploadUrlInput!): SignedUploadUrl!
  createClub(createClubInput: CreateClubInput!): Boolean!
  updateClub(updateClubInput: UpdateClubInput!): Club!
  removeClub(id: String!): Club!
  createCity(createCityInput: CreateCityInput!): City!
  updateCity(updateCityInput: UpdateCityInput!): City!
  removeCity(id: Int!): City!
}

input SignInInput {
  """User phone number"""
  email: String!

  """User password"""
  password: String!
}

input CreateUserInput {
  """User fullname"""
  fullname: String!

  """User phone number"""
  email: String!

  """User role"""
  role: UserRoles!

  """User password"""
  password: String!

  """User active status"""
  userStatus: UserStatus!

  """User contact information"""
  contact: ContactInput!

  """Whether user has accepted terms and conditions"""
  acceptedTermsAndConditions: Boolean!

  """Whether user wants to receive discounts, royalty offers and updates"""
  subscribeToUpdates: Boolean!
}

input ContactInput {
  """Country code (e.g., +1, +91)"""
  countryCode: String!

  """Phone number"""
  phone: String!
}

input SignedUploadUrlInput {
  key: String!
  contentType: String!
  expiresIn: Float
}

input CreateClubInput {
  """Club name"""
  name: String!

  """Club description"""
  description: String!

  """Club category IDs"""
  categories: [String!]!

  """Club status"""
  status: ClubStatus!

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]!

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  openingHours: OpeningHoursInput

  """Whether club is featured"""
  featured: Boolean!

  """Club rating (0-5)"""
  rating: Float!
}

input AddressInput {
  """Address"""
  address: String!

  """Coordinates"""
  coordinates: CoordinatesInput!

  """Metro line"""
  metroLine: String

  """Metro station"""
  metroStation: String
}

"""Inputs"""
input CoordinatesInput {
  """Latitude"""
  latitude: Float!

  """Longitude"""
  longitude: Float!
}

input OpeningHoursInput {
  """
  Array of day timings. Each day can have opening time only or both opening and closing times in 24hr HHMM format
  """
  schedule: [DayTimingInput!]!
}

input DayTimingInput {
  """Day of the week"""
  day: DayOfWeek!

  """
  Opening hours in 24hr format [openTime, closeTime]. Single number for opening time only, two numbers for open and close times. Times are in HHMM format (e.g., 900 for 9:00, 1730 for 17:30). Supports formats that can be converted by time.utils: 900, 1730, etc.
  """
  timings: [Float!]!
}

input UpdateClubInput {
  """Club name"""
  name: String

  """Club description"""
  description: String

  """Club category IDs"""
  categories: [String!]

  """Club status"""
  status: ClubStatus

  """Club logo URL"""
  logo: String

  """Club cover image URL"""
  coverImage: String

  """Array of club image URLs"""
  images: [String!]

  """Club contact information (phone)"""
  contact: ContactInput

  """Club address"""
  address: AddressInput

  """Opening hours"""
  openingHours: OpeningHoursInput

  """Whether club is featured"""
  featured: Boolean

  """Club rating (0-5)"""
  rating: Float
  id: String!
}

input CreateCityInput {
  """Example field (placeholder)"""
  exampleField: Float!
}

input UpdateCityInput {
  """Example field (placeholder)"""
  exampleField: Float
  id: Float!
}